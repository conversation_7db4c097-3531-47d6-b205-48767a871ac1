parameters:
  - name: devopsFolderPath
    type: string
  - name: chartName
    type: string
  - name: dependsOn
    type: string

jobs:
  - deployment: Deploy_Mock
    displayName: 'Deploy Mock'
    environment: 'poet-facade-mock'
    dependsOn: ${{ parameters.dependsOn }}
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: self
            - template: ../step-templates/kubelogin.yaml
              parameters:
                serviceConnection: SC-PODS-NP
                targetCluster: AKS-EUS-MK-01
                targetResourceGroup: RG-INFRA-MK

            - template: ../step-templates/helm-deploy.yaml
              parameters:
                serviceConnection: SC-PODS-NP
                targetCluster: AKS-EUS-MK-01
                environment: mock
                namespace: poet-facade
                chartName: ${{ parameters.chartName }}
                devopsFolderPath: ${{ parameters.devopsFolderPath }}
