pool: Azure Pipelines
trigger:
  paths:
    include:
      - devops
      - src
      - build.gradle.kts
      - settings.gradle.kts
  branches:
    include:
      - main

variables:
  isMain: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  serviceConnectionNP: SC-PODS-NP
  serviceConnectionPD: SC-PODS-PD
  devopsFolderPath: devops
  serviceName: poet-facade
  chartName: poet-facade

stages:
  - stage: Build_Test_Push_Docker_Image
    displayName: 'Build and Test Gradle Project, Push Docker Image'
    jobs:
      - job: Build_And_Test
        displayName: 'Build and Test Gradle Project, Push Docker Image'
        steps:
          - script:
              echo 'Building image $(Build.SourceVersion)'
          - task: Gradle@3
            displayName: Run Tests
            inputs:
              cwd: '$(System.DefaultWorkingDirectory)'
              gradleWrapperFile: '$(System.DefaultWorkingDirectory)/gradlew'
              javaHomeOption: 'JDKVersion'
              jdkVersionOption: '1.17'
              jdkArchitectureOption: 'x64'
              publishJUnitResults: true
              gradleOptions: '-Xmx4096M'
              testResultsFiles: '**/TEST-*.xml'
              testRunTitle: "Backend Tests"
              tasks: 'build'
              options: '--parallel'
          - task: Docker@2
            inputs:
              buildContext: '$(System.DefaultWorkingDirectory)'
              command: 'buildAndPush'
              Dockerfile: '$(System.DefaultWorkingDirectory)/$(devopsFolderPath)/Dockerfile'
              containerRegistry: podscreusdv
              repository: $(serviceName)
              tags: |
                $(Build.SourceVersion)

  - stage: Approval
    displayName: 'Manual Approval'
    dependsOn: Build_Test_Push_Docker_Image
    jobs:
      - job: Approval
        displayName: 'Approval Job'
        pool: server
        steps:
          - task: ManualValidation@0
            condition: and(succeeded(), eq(variables.isMain, 'false'))
            timeoutInMinutes: 1440 # task times out in 1 day
            inputs:
              instructions: 'Do you really want to deploy $(Build.SourceBranch) to Dev?'

  - stage: Deploy_Dev
    displayName: 'Deploy to Dev'
    dependsOn: Approval
    jobs:
      - template: ./deploy-templates/deploy-dev.yaml
        parameters:
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Test
    displayName: 'Deploy to Test'
    dependsOn: Deploy_Dev
    jobs:
      - template: ./deploy-templates/create-new-image.yaml
        parameters:
          jobName: Create_New_Image
          serviceName: $(serviceName)
          serviceConnection: $(serviceConnectionNP)
          targetAzureSubscriptionId: 805be479-ee7f-4c01-aed8-8267c4ff0664
          previousAzureSubscriptionId: 805be479-ee7f-4c01-aed8-8267c4ff0664
          previousImageResourceGroup: RG-INFRA-DV
          previousContainerRegistry: podscreusdv
          targetContainerRegistry: podscreuste
      - template: ./deploy-templates/deploy-test.yaml
        parameters:
          dependsOn: Create_New_Image
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Stage
    displayName: 'Deploy to Stage EUS and WUS'
    dependsOn: Deploy_Test
    jobs:
      - template: ./deploy-templates/create-new-image.yaml
        parameters:
          jobName: Create_New_Image
          serviceName: $(serviceName)
          serviceConnection: $(serviceConnectionNP)
          targetAzureSubscriptionId: 805be479-ee7f-4c01-aed8-8267c4ff0664
          previousAzureSubscriptionId: 805be479-ee7f-4c01-aed8-8267c4ff0664
          previousImageResourceGroup: RG-INFRA-TE
          previousContainerRegistry: podscreuste
          targetContainerRegistry: podscreusst
      - template: ./deploy-templates/deploy-stage-eus.yaml
        parameters:
          dependsOn: Create_New_Image
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)
      - template: ./deploy-templates/deploy-stage-wus.yaml
        parameters:
          dependsOn: Create_New_Image
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Mock
    displayName: 'Deploy to Mock'
    dependsOn: Deploy_Stage
    jobs:
      - template: ./deploy-templates/create-new-image.yaml
        parameters:
          jobName: Create_New_Image
          serviceName: $(serviceName)
          serviceConnection: $(serviceConnectionNP)
          targetAzureSubscriptionId: 805be479-ee7f-4c01-aed8-8267c4ff0664
          previousAzureSubscriptionId: 805be479-ee7f-4c01-aed8-8267c4ff0664
          previousImageResourceGroup: RG-INFRA-ST
          previousContainerRegistry: podscreusst
          targetContainerRegistry: podscreusmk
      - template: ./deploy-templates/deploy-mock.yaml
        parameters:
          dependsOn: Create_New_Image
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Production_EUS
    displayName: 'Deploy to Production EUS'
    dependsOn: Deploy_Mock
    jobs:
      - template: ./deploy-templates/create-new-image.yaml
        parameters:
          jobName: Create_New_Image
          serviceName: $(serviceName)
          serviceConnection: $(serviceConnectionPD)
          targetAzureSubscriptionId: 2a71f9dc-3c1b-48dd-bb6b-c3b9e9456fc1
          previousAzureSubscriptionId: 805be479-ee7f-4c01-aed8-8267c4ff0664
          previousImageResourceGroup: RG-INFRA-ST
          previousContainerRegistry: podscreusst
          targetContainerRegistry: podscreuspd
      - template: ./deploy-templates/deploy-prod-eus.yaml
        parameters:
          dependsOn: Create_New_Image
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Production_WUS
    displayName: 'Deploy to Production WUS'
    dependsOn: Deploy_Production_EUS
    jobs:
      - template: ./deploy-templates/deploy-prod-wus.yaml
        parameters:
          dependsOn: ""
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)
